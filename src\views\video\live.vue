<template>
  <div class="details-wrap">
    <!-- <van-loading v-if="loading" type="spinner" color="#1989fa" /> -->
    <div class="box"  ref="inPlayer">
      <in-video :cover="infoData.coverUrl" 
        :url="infoData.liveUrl" 
        :isLive="isLive" 
        :status="playStatus"
        :noNeedShowMsg="playStatus === 'PREDICTION'"
      >
        <div @click="showPopUpFn" class="moreLive" >
          <img src="@/assets/images/live-icon.png" alt="" >
          <span>{{ $t('video.moreLive') }}</span>
        </div>
        <div class="prediction" v-if="playStatus === 'PREDICTION'">
          <div class="prediction-content">
            <div>{{ $t('video.distance') }} {{ moment(infoData.livingTime).format('MM-DD HH:mm') }}</div>
            <div class="countdown">{{ countdownText }}</div>
            <div class="botton-content">
              <div class="appoint" @click="toAppoint">{{ $t('video.appoint') }}</div>
            </div>
          </div>
        </div>
      </in-video>
      <!-- <div class="translate-icon" v-if="needShowTranslate && !isInEddidApp" @click="changeTranslate">
        <img :src="isTranslate ? translateIcon : translatedIcon" alt="">
      </div> -->
      <div class="content">
        <div class="head">
          <div class="author" v-if="infoData.streamer">
            <img class="avatar" :src="infoData.streamer?.avatarUrl" alt="">
            <span>{{ streamerName }}</span>
          </div>
        </div>
        <div class="title">{{ infoData['title' + suffix] || "" }}</div>
        <div class="live-stream-introduction">{{$t('video.liveStreamIntroduction')}}</div>
        <div class="comment default-content-style" v-html="infoData['introduction' + suffix]" ></div>
        <div class="disclaimer">
          <div>*{{ $t('video.investmentRisks')}}*</div>
          <div>{{$t('video.disclaimer')}}</div>
        </div>
      </div>
    </div>
    <moveLive v-model="rightVisible" @toDetail="toDetail" :suffix="suffix" :categoryId="infoData.category?.categoryId"/>
  </div>
</template>
<script>
import { mapState } from "vuex";
import Mixins from "../../mixins";
import InVideo from "@/components/in-video.vue";
import moveLive from "./components/move-live.vue";
import { getLiveDetail, getLiveAddress, buryVideoThePoint } from '@/api/video'
import { debounce, mockSetInterval } from '@/utils/util'
import translatedIcon from '@/assets/images/translated.png';
import translateIcon from '@/assets/images/translate_dark.png';
import share from '@/assets/images/share.png';
import moment from "moment";
export default {
  mixins: [ Mixins ],
  data() {
    return {
      translatedIcon,
      translateIcon,
      share,
      moment,
      loading: false,
      liveId: '',
      infoData: {
        // title: '投资笔记特别场 - 解构加密货币大升之谜 |主持：SaSa | 嘉宾：萧佩铃 三星ETF 投资策略师 | 陈政深 艾德金融董事',
        // author: 'Eddid Channel',
        // releaseTime: '2024-03-28',
        // coverUrl: 'https://middleware-information-qa-public.oss-cn-shenzhen.aliyuncs.com/9277e7e3-cfe5-43bb-a755-d3ddbee4d7aa.jpg?t=001.jpeg',
        // content: '*投资有风险, 交易需谨慎   #比特币 #国泰 #美股 #基金 #ETF #强积金 #旅游股 #中国经济 ',
        // url: 'https://lf3-static.bytednsdoc.com/obj/eden-cn/nupenuvpxnuvo/xgplayer_doc/xgplayer-demo.mp4'
      },
      isLive: true,
      recommendList: [],
      rightVisible: false,
      isTranslate: false,
      debounceShowPopUp: null,
      langMap: {
        "zh-hans": "CN",
        "zh-hant": "TC",
        "en": "EN",
      },
      countdownText: '00 天 00 时 00 分 00 秒'

    }
  },
  components:{
    InVideo,
    moveLive
  },
  watch: {
    '$route'(to, from) {
      // 仅当 id 变化时触发
      if (to.query.liveId !== from.query.liveId) {
        this.$refs.inPlayer && this.$refs.inPlayer.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
        this.initData()
      }
    }
    // isLogin: {
    //   immediate: true,
    //   handler(val){
    //     if(val){
    //       this.getToken();
    //     }
    //   }
    // }
  },
  computed: {
    ...mapState(['globalLang', 'globalTheme']),
    needShowTranslate(){
      return this.infoData.translateLanguage && this.infoData.translateLanguage?.indexOf(this.langMap[this.globalLang]) > -1
    },
    suffix(){
      return this.isTranslate ? 'Origin' : ''
    },
    streamerName(){
      return this.infoData?.streamer?.[`streamerName${this.suffix}`] || '--'
    },
    isInEddidApp(){
      return this.$jsBridge.isSupported('getAppInfo');
    },
    playStatus(){
      if(this.infoData.publishStage === 'UNSHELVE'){
        return 'DOWN'
      }else {
        return this.infoData.liveStatus
      }
    }
  },
  created() {

  },
  mounted(){
    this.initData()
  },
  methods: {
    showPopUpFn(){
      this.debounceShowPopUp()
    },
    async initData(){
      this.liveId = this.$route.query.id || this.$route.query.liveId || 'abe36344-08ce-476c-9374-d76a955ae001'
      this.debounceShowPopUp = debounce(this.showPopUp, 300, true, false)
      this.loading = true
      try {
        await this.getLiveDetail()
        // await this.buryVideoThePoint()
        this.getLiveAddress()
      } catch (error) {
        console.error('Error initializing data:', error)
      } finally {
        this.loading = false
      }
    },
    async getLiveAddress(){
      try {
        const res = await getLiveAddress(this.liveId)
        this.infoData.liveUrl = res.data ? res.data.hlsUrl : ''
      } catch (error) {
        console.error('Error getting live address:', error)
        this.infoData.liveUrl = ''
      }
    },
    async getLiveDetail(){
      try {
        const res = await getLiveDetail(this.liveId)
        let data = res.data ? res.data : {}
        // this.isLive = true
        // data.videoUri = 'https://ems-pull-qa.eddidyzt.com/ems/d02de31545aa472c9d16df68bcaccf71.m3u8?auth_key=1750245887-54ecce9e8db840d69970501578f3634f-0-044a2d18f791ae978104959ead3a2ba8'

        this.infoData = data
      } catch (error) {
        console.error('Error getting live detail:', error)
        this.infoData = {}
      }
    },
    async buryVideoThePoint(){
      let params ={
        "author": this.infoData.videoAuthor,
        "columnId": this.infoData.relatedColumn,
        "liveId": this.infoData.liveId,
        "title": this.infoData.videoTitle
      }
      await buryVideoThePoint(params).then(res =>{

      })
    },
    toDetail(item){
      this.$router.push({
        path: '/video/live',
        query: {
          id: item.liveId
        }
      })
      this.rightVisible = !this.rightVisible
    },
    showPopUp(){
      this.rightVisible = !this.rightVisible
    },
    changeTranslate(){
      this.isTranslate =  !this.isTranslate
      this.setBar()
    },
    setBar(){
      let arr = [
        {
          showType: 'img',
          img: this.isTranslate ? this.translateIcon : this.translatedIcon,
          callback: () => {
            this.changeTranslate()
          }
        },
        {
          showType: 'img',
          img: share,
          callback: () => {
            this.onShared();
          }
        }
      ]
      this.$jsBridge.run('setCommonNavBar', {
        right: arr
      })
    },
    onShared() {
      const { title, introduction, coverUrl } = this.infoData;
      const url = window.location.href // 过滤access_token、source、theme参数;
      this.$jsBridge.run('startShareUrlAndPicture', {
        type: 'B',
        title: title,
        content: introduction,
        imageUrl: coverUrl,
        url: `${url}&source=web&pageType=researchLiveDetail`, // sourc  e用于识别来源: web、app
        pageType: 'inforDetail',
        contentType: '研究院',
        callback: (body) => {
          // this.$sensor.common.contentShare({
          //   content_type: informationSource === 'EDDID_REALTIME' ? '艾德自研' : '第三方资讯', // '资讯详情页',
          //   content_title: realtimeTitle,
          //   activity_url: window.location.href,
          //   share_channel: body.channel
          // })
        }
      });
    },
    toAppoint(){
      // this.$jsBridge.run('startEdWeb', {
      //   url: '/live/appoint',
      //   query: {
      //     liveId: this.liveId
      //   }
      // })
    },
    setCountdownText(startTime) {
      const diff = startTime - new Date().getTime()
      const d = Math.floor(diff / (60 * 1000 * 60 * 24))
      const h = Math.floor(diff / (60 * 1000 * 60)) - d * 24
      const hdiff = diff % (60 * 1000 * 60)
      const m = Math.floor(hdiff / (60 * 1000))
      const mdiff = diff % (60 * 1000)
      let s = Math.ceil(mdiff / 1000) - 1
      let countdownText = ''
      countdownText += `${d.addZero()} ${this.$t('天')} `
      countdownText += `${h.addZero()} ${this.$t('时')} `
      countdownText += `${m.addZero()} ${this.$t('分')} `
      countdownText += `${s.addZero()} ${this.$t('秒')}`
      this.countdownText = countdownText
    },
  },
  countdown() {
    if (!this.checkIsUnStart()) {
      return
    }
    this.timer = mockSetInterval(() => {
      this.checkIsUnStart()
    }, 1000)
  },
  beforeUnmount(){
    this.debounceShowPopUp.cancel()
  }
  
}
</script>
<style lang="scss" scoped>
.details-wrap{
  height: 100vh;
  font-size: 12px;
  line-height: 18px;
  background: var(--background);
  .content{
    padding: 12px 16px 10px;
    height: calc(100vh - 242px);
    overflow-y: auto;
    overflow-x: hidden;
    margin-top: 10px;
  }
  .title {
    color: var(--text_3rd);
    font-size: 13px;
    margin: 4px 0;
    overflow: hidden; //超出文本隐藏
    text-overflow: ellipsis; ///超出部分省略号显示
    display: -webkit-box; //弹性盒模型
    -webkit-box-orient: vertical; //上下垂直
    -webkit-line-clamp: 2; //自定义行数
  }
  .author, .comment {
    color: var(--text_2nd);
    font-size: 12px;
  }
  .comment{
    margin-top: 12px;
  }
  .disclaimer{
    background: var(--gray_05);
    padding: 12px;
    color: var(--text_3rd);
    margin-top: 12px;
  }
  .moreLive {
    position: absolute;
    top: 8px;
    right: 10px;
    min-width: 40px;
    height: 20px;
    z-index: 10;
    font-size: 12px;
    color: var(--background);
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--mask);
    border-radius: 10px;
    // border: 1px solid rgba(229, 229, 229, 0.28);
    border-right: none;
    padding: 5px 8px 5px 11px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    img {
      width: 24px;
      height: 22px;
    }
  }
  .head{
    margin-bottom: 16px;
  }
  .avatar{
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 8px;
    vertical-align: middle;
  }
  .live-stream-introduction{
    font-size: 17px;
    font-weight: 600;
    padding: 16px 16px 16px 0;
    color: var(--text_1st);
    border-bottom: 1px solid var(--line_01);
  }
  .translate-icon{
    height: 30px;
    text-align: right;
    img{
      height: 100%;
      margin-right: 10px;
      margin-top: 10px;
    }
  }
  .prediction{
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 9;
    background: rgba(0, 0, 0, 0.6);
    color: var(--background);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .prediction-content{
    text-align: center;
  }
  .countdown{
    margin: 10px 0 21px;
    font-size: 18px;
  }
  .botton-content{
    display: flex;
    justify-content: center;
  }
  .appoint{
    width: fit-content;
    background: var(--brand_02);
    padding: 9px 38px;
    border-radius: 20px;
  }
}
</style>
