<template>
  <div class="details-wrap">
    <van-loading v-if="loading" type="spinner" color="#1989fa" />
    <div class="box" v-else ref="inPlayer">
      <in-video :cover="infoData.coverUrl" :url="infoData.liveUrl" :isLive="isLive">
        <div @click="showPopUpFn" class="moreLive">
          <img src="@/assets/images/live-icon.png" alt="" >
          <span>{{ $t('video.moreLive') }}</span>
        </div>
      </in-video>
      
      <moveLive v-model="rightVisible" @toDetail="toDetail"/>
      <div class="content">
        <div class="head">
          <div class="author" v-if="infoData.streamer">
            <img class="avatar" :src="infoData.streamer?.avatarUrl" alt="">
            <span>{{ `${infoData?.streamer['streamerName' + suffix] || '--'}` }}</span>
          </div>
        </div>
        <div class="title">{{ infoData.title || "" }}</div>
        <div class="comment default-content-style" v-html="infoData.videoIntroduction" ></div>
        <div class="disclaimer">
          <div>*{{ $t('video.investmentRisks')}}*</div>
          <div>{{$t('video.disclaimer')}}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import Mixins from "../../mixins";
import InVideo from "@/components/in-video.vue";
import moveLive from "./components/move-live.vue";
import { getLiveDetail, getLiveAddress, buryVideoThePoint } from '@/api/video'
import { debounce } from '@/utils/util'
export default {
  mixins: [ Mixins ],
  data() {
    return {
      loading: false,
      liveId: '',
      infoData: {
        // title: '投资笔记特别场 - 解构加密货币大升之谜 |主持：SaSa | 嘉宾：萧佩铃 三星ETF 投资策略师 | 陈政深 艾德金融董事',
        // author: 'Eddid Channel',
        // releaseTime: '2024-03-28',
        // coverUrl: 'https://middleware-information-qa-public.oss-cn-shenzhen.aliyuncs.com/9277e7e3-cfe5-43bb-a755-d3ddbee4d7aa.jpg?t=001.jpeg',
        // content: '*投资有风险, 交易需谨慎   #比特币 #国泰 #美股 #基金 #ETF #强积金 #旅游股 #中国经济 ',
        // url: 'https://lf3-static.bytednsdoc.com/obj/eden-cn/nupenuvpxnuvo/xgplayer_doc/xgplayer-demo.mp4'
      },
      isLive: true,
      recommendList: [],
      rightVisible: false,
      isTranslate: false,
      debounceShowPopUp: null

    }
  },
  components:{
    InVideo,
    moveLive
  },
  watch: {
    '$route'(to, from) {
      // 仅当 id 变化时触发
      if (to.query.liveId !== from.query.liveId) {
        this.$refs.inPlayer && this.$refs.inPlayer.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
        this.initData()
      }
    }
    // isLogin: {
    //   immediate: true,
    //   handler(val){
    //     if(val){
    //       this.getToken();
    //     }
    //   }
    // }
  },
  computed: {
    ...mapState(['globalLang', 'globalTheme']),
    needShowTranslate(){
      return this.infoData.translateLanguage && this.infoData.translateLanguage.indexOf(this.langMap[this.globalLang]) > -1
    },
    suffix(){
      return this.isTranslate ? 'Origin' : ''
    },
  },
  created() {

  },
  mounted(){
    this.initData()
  },
  methods: {
    showPopUpFn(){
      this.debounceShowPopUp()
    },
    async initData(){
      this.liveId = this.$route.query.id || this.$route.query.liveId || 'abe36344-08ce-476c-9374-d76a955ae001'
      this.debounceShowPopUp = debounce(this.showPopUp, 300, true, false)
      this.loading = true
      try {
        await this.getLiveDetail()
        await this.buryVideoThePoint()
        this.getLiveAddress()
      } catch (error) {
        console.error('Error initializing data:', error)
      } finally {
        this.loading = false
      }
    },
    getLiveAddress(){
      getLiveAddress(this.liveId).then(res => {
        this.infoData.liveUrl = res.data ? res.data.hlsUrl : {}
      })
    },
    async getLiveDetail(){
      
      await getLiveDetail(this.liveId).then((res) => {
        let data = res.data ? res.data : {}
        // this.isLive = true
        // data.videoUri = 'https://ems-pull-qa.eddidyzt.com/ems/d02de31545aa472c9d16df68bcaccf71.m3u8?auth_key=1750245887-54ecce9e8db840d69970501578f3634f-0-044a2d18f791ae978104959ead3a2ba8'
        
        this.infoData = data
      })
    },
    async buryVideoThePoint(){
      let params ={
        "author": this.infoData.videoAuthor,
        "columnId": this.infoData.relatedColumn,
        "liveId": this.infoData.liveId,
        "title": this.infoData.videoTitle
      }
      await buryVideoThePoint(params).then(res =>{

      })
    },
    toDetail(item){
      this.$router.push({
        path: '/video/live',
        query: {
          id: item.liveId
        }
      })
      this.rightVisible = !this.rightVisible
    },
    showPopUp(){
      this.rightVisible = !this.rightVisible
    }
  },
  beforeUnmount(){
    this.debounceShowPopUp.cancel()
  }
  
}
</script>
<style lang="scss" scoped>
.details-wrap{
  overflow-y: auto;
  overflow-x: hidden;
  height: 100vh;
  font-size: 12px;
  line-height: 18px;
  background: var(--background);
  .content{
    padding: 12px 16px 10px;
  }
  .title {
    color:var(--text_1st);
    font-weight: 600;
    font-size: 16px;
    margin: 4px 0;
    line-height: 26px;
  }
  .author, .comment {
    color: var(--text_2nd);
    font-size: 12px;
  }
  .comment{
    margin-top: 12px;
  }
  .disclaimer{
    background: var(--gray_05);
    padding: 12px;
    color: var(--text_3rd);
    margin-top: 12px;
  }
  .moreLive {
    position: absolute;
    top: 8px;
    right: 10px;
    min-width: 40px;
    height: 20px;
    z-index: 8;
    font-size: 12px;
    color: var(--background);
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--mask);
    border-radius: 10px;
    // border: 1px solid rgba(229, 229, 229, 0.28);
    border-right: none;
    padding: 5px 8px 5px 11px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    img {
      width: 24px;
      height: 22px;
    }
  }
  .avatar{
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 8px;
  }
}
</style>
