import axios from "@/utils/request";
const baseUrl = '/open/operation'
export function getVideoDetail(params) { 
  return axios.get(`${baseUrl}/research/custom/video/detail`, { params })
}
export function getRecVideoList(params) { 
  return axios.get(`${baseUrl}/research/custom/video/list`, { params })
}
export function buryVideoThePoint(params) {
  return axios.post(`${baseUrl}/news/analysis/report/subdirectory/video`, params)
}

// 直播推荐
export function getLiveList (params) {
   return axios.get(`/open/ems/live/liveAndVideos`, { params })
}
// 直播详情
export function getLiveDetail (id, params) {
   return axios.get(`/open/ems/live/liveAndPreview/${id}`, { params })
}
// 回放详情
export function getPlaybackDetail (id, params) {
   return axios.get(`/open/ems/live/video/${id}`, { params })
}
// 获取直播地址
export function getLiveAddress (id, params) {
   return axios.get(`/open/ems/live/liveAddress/${id}`, { params })
}

