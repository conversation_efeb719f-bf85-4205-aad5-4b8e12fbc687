<template>
  <div v-show="isPopupShow" class="md-popup" :class="[
      hasMask ? 'with-mask' : '',
      largeRadius ? 'large-radius' : '',
      position
    ]">
    <transition name="md-mask-fade">
      <div v-show="hasMask && isPopupBoxShow" @click="$_onPopupMaskClick" class="md-popup-mask"></div>
    </transition>
    <transition :name="transitionName" @before-enter="$_onPopupTransitionStart" @before-leave="$_onPopupTransitionStart"
      @after-enter="$_onPopupTransitionEnd" @after-leave="$_onPopupTransitionEnd">
      <div v-show="isPopupBoxShow" class="md-popup-box">
        <slot></slot>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'md-popup',
  props: {
    position: {
      type: String,
      default: 'center',
    },
    transition: {
      type: String,
      default: '',
    },
    preventScroll: {
      type: Boolean,
      default: false,
    },
    preventScrollExclude: {
      type: [String, Function],
      default() {
        return ''
      },
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    hasMask: {
      type: Boolean,
      default: true,
    },
    maskClosable: {
      type: Boolean,
      default: true,
    },
  },

  emits: ['update:modelValue', 'before-hide', 'before-show', 'hide', 'show', 'maskClick'],

  data() {
    return {
      // controle popup mask & popup box
      isPopupShow: false,
      // controle popup box
      isPopupBoxShow: false,
      // transtion lock
      isAnimation: false,
      largeRadius: false,
    }
  },

  watch: {
    modelValue(val) {
      if (val) {
        if (this.isAnimation) {
          setTimeout(() => {
            this.$_showPopupBox()
          }, 50)
        } else {
          this.$_showPopupBox()
        }
      } else {
        this.$_hidePopupBox()
      }
    },
  },

  mounted() {
    this.modelValue && this.$_showPopupBox()
  },

  computed: {
    transitionName() {
      if (this.transition) {
        return this.transition
      }
      
      // Default transitions based on position
      const transitionMap = {
        'top': 'md-slide-down',
        'bottom': 'md-slide-up',
        'left': 'md-slide-right',
        'right': 'md-slide-left'
      }
      
      return transitionMap[this.position] || 'md-fade'
    }
  },

  methods: {
    $_showPopupBox() {
      this.isPopupShow = true
      this.isAnimation = true
      this.isPopupBoxShow = true
    },
    $_hidePopupBox() {
      this.isAnimation = true
      this.isPopupBoxShow = false
      this.$emit('update:modelValue', false)
    },

    // MARK: event handler
    $_onPopupTransitionStart() {
      if (!this.isPopupBoxShow) {
        this.$emit('beforeHide')
        this.$emit('before-hide')
      } else {
        this.$emit('beforeShow')
        this.$emit('before-show')
      }
    },
    $_onPopupTransitionEnd() {
      /* istanbul ignore next */
      if (!this.isAnimation) {
        return
      }

      /* istanbul ignore next */
      if (!this.isPopupBoxShow) {
        // popup hide after popup box finish animation
        this.isPopupShow = false
        this.$emit('hide')
      } else {
        this.$emit('show')
      }

      /* istanbul ignore next */
      this.isAnimation = false
    },
    $_onPopupMaskClick() {
      if (this.maskClosable) {
        this.$_hidePopupBox()
        this.$emit('maskClick')
      }
    },
  },
}

</script>

<style lang="scss">
.md-popup {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  position: fixed;
  display: flex;
  pointer-events: none;
  z-index: 10;
  &.center {
    align-items: center;
    justify-content: center;
  }
  &.top {
    flex-direction: column;
    justify-content: flex-start;
    .md-popup-box {
      width: 100%;
    }
  }
  &.bottom {
    flex-direction: column;
    justify-content: flex-end;
    .md-popup-box {
      width: 100%;
    }
  }
  &.left {
    justify-content: flex-start;
    .md-popup-box {
      height: 100%;
    }
  }
  &.right {
    justify-content: flex-end;
    .md-popup-box {
      height: 100%;
    }
  }
  &.inner-popup {
    .md-popup-box {
      background-color: rgba(37,38,45,.3);
      border-radius: 10px 10px 0 0;
    }
  }
  &.large-radius {
    &.inner-popup {
      .md-popup-box {
        border-radius: 10px 10px
          0 0;
      }
    }
  }
}
.md-popup-mask {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  position: absolute;
  pointer-events: auto;
  z-index: 12;
  background-color: rgba(37,38,45,.7);
}
.md-popup-box {
  position: relative;
  pointer-events: auto;
  z-index: 14;
  max-width: 100%;
  max-height: 100%;
  overflow: auto;
}
.md-mask-fade-enter,
.md-mask-fade-leave-to {
  opacity: 0.01;
}
.md-mask-fade-enter-active,
.md-mask-fade-leave-active {
  transition: opacity 250ms;
}
.md-slide-left-enter-from,
.md-slide-left-leave-to {
  transform: translate3d(100%, 0, 0);
}

.md-slide-left-enter-active {
  transition: transform 300ms cubic-bezier(0.165, 0.84, 0.44, 1);
}

.md-slide-left-leave-active {
  transition: transform 250ms cubic-bezier(0.165, 0.84, 0.44, 1);
}

.md-slide-right-enter-from,
.md-slide-right-leave-to {
  transform: translate3d(-100%, 0, 0);
}

.md-slide-right-enter-active {
  transition: transform 300ms cubic-bezier(0.165, 0.84, 0.44, 1);
}

.md-slide-right-leave-active {
  transition: transform 250ms cubic-bezier(0.165, 0.84, 0.44, 1);
}

.md-slide-up-enter-from,
.md-slide-up-leave-to {
  transform: translate3d(0, 100%, 0);
}

.md-slide-up-enter-active {
  transition: transform 300ms cubic-bezier(0.165, 0.84, 0.44, 1);
}

.md-slide-up-leave-active {
  transition: transform 250ms cubic-bezier(0.165, 0.84, 0.44, 1);
}

.md-slide-down-enter-from,
.md-slide-down-leave-to {
  transform: translate3d(0, -100%, 0);
}

.md-slide-down-enter-active {
  transition: transform 300ms cubic-bezier(0.165, 0.84, 0.44, 1);
}

.md-slide-down-leave-active {
  transition: transform 250ms cubic-bezier(0.165, 0.84, 0.44, 1);
}

.md-fade-enter-from,
.md-fade-leave-to {
  opacity: 0;
}

.md-fade-enter-active,
.md-fade-leave-active {
  transition: opacity 300ms cubic-bezier(0.165, 0.84, 0.44, 1);
}
</style>
