import { jsBridge } from '@/utils/jsBridge';
import router from "@/router";
function getQueryType(key) {
  if (key.endsWith("[]")) return "ARRAY";
  if (key.endsWith("{}")) return "JSON";
  return "DEFAULT";
}
export function urlParse(query) {
  if (!query) {
    return {};
  }
  query = query.replace(/^\?/, "");
  const queryArr = query.split("&");
  const result = {};
  queryArr.forEach((q) => {
    let [key, value] = q.split("=");
    try {
      value = decodeURIComponent(value || "").replace(/\+/g, " ");
      key = decodeURIComponent(key || "").replace(/\+/g, " ");
    } catch (e) {
      // 非法
      console.log(e);
      return;
    }
    const type = getQueryType(key);
    switch (type) {
      case "ARRAY":
        key = key.replace(/\[\]$/, "");
        if (!result[key]) {
          result[key] = [value];
        } else {
          result[key].push(value);
        }
        break;
      case "JSON":
        key = key.replace(/\{\}$/, "");
        value = JSON.parse(value);
        result.json = value;
        break;
      default:
        result[key] = value;
    }
  });
  return result;
}

export const getBas64 = (url, outputFormat = 'image/png') => {
  return new Promise(function (resolve, reject) {
    let canvas = document.createElement('CANVAS');
    let ctx = canvas.getContext('2d');
    let img = new Image;

    img.crossOrigin = 'Anonymous';
    img.onload = function () {
      canvas.height = img.height;
      canvas.width = img.width;
      ctx.drawImage(img, 0, 0);
      let dataURL = canvas.toDataURL(outputFormat);
      canvas = null;
      resolve(dataURL);
    };
    img.src = url+'?t='+new Date().valueOf()
  })
}
export const startEdWeb = function (query = {}) {
  if(jsBridge.isSupported('startEdWeb')) {
    if(!query.url.indexOf('http')){
      query.url = `${window.location.origin}${query.url }`
    }
    query.url = `${window.location.origin}${query.url }`
    jsBridge.run('startEdWeb', query);
  } else {
    router.push(query.url);
  }
}

// 创建一个名为 HeadManager 的头部管理插件

export const HeadManager = {
  install(Vue) {
    Vue.mixin({
      data() {
        return {
          pageTitle: '',
          metaTags: [
            {
              name: '',
              content: ''
            },
            // 可以添加更多的 meta 标签
          ]
        };
      },
      computed: {
        // 使用计算属性来设置头部信息
        head() {
          return {
            title: this.pageTitle,
            meta: this.metaTags
          };
        }
      },
      watch: {
        // 监听页面标题变化
        pageTitle(newTitle) {
          this.updateTitle(newTitle);
        },
        // 监听 meta 标签变化
        metaTags(newMetaTags) {
          this.updateMetaTags(newMetaTags);
        }
      },
      methods: {
        // 更新页面标题
        updateTitle(newTitle) {
          document.title = newTitle;
        },
        // 更新 meta 标签
        updateMetaTags(newMetaTags) {
          // 移除旧的 meta 标签
          const head = document.head;
          const existingMetaTags = head.querySelectorAll('meta[name]');
          existingMetaTags.forEach(tag => tag.remove());

          // 添加新的 meta 标签
          newMetaTags.forEach(tag => {
            const metaTag = document.createElement('meta');
            metaTag.setAttribute('name', tag.name);
            metaTag.setAttribute('content', tag.content);
            head.appendChild(metaTag);
          });
        }
      },
      created() {
        // 在页面创建时，设置默认头部信息
        this.updateTitle(this.pageTitle);
        this.updateMetaTags(this.metaTags);
      }
    });
  }
};
// 防抖函数
export const debounce = function (func, wait, immediate, clear) {
  console.log(clear,'sss')
  let timeout, args, context, timestamp, result;
  
  const later = function () {
    // 据上一次触发时间间隔
    const last = Date.now() - timestamp;

    // 上次被包装函数被调用时间间隔last小于设定时间间隔wait
    // if(clear){
    //   timeout = null
    //   clearTimeout(timeout)
    // }
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };
  const debounced =  function () {
    context = this;
    args = arguments;
    timestamp = Date.now();

    const callNow = immediate && !timeout;

    // 如果clear为true，清除之前的定时器
    if (clear && timeout) {
      clearTimeout(timeout);
      timeout = null;
    }

    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait);

    console.log(immediate, clear, timeout, callNow, "callNowcallNow");

    if (callNow) {
      result = func.apply(context, args);
      context = args = null;
    }
    return result;
  };
  debounced.cancel = function () {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
      context = args = null;
    }
  };
  return debounced;
};

// 在你的 Vue 应用中使用这个插件

