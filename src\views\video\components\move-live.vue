<template>
  <popup
    v-model="visible"
    position="right"
  >
    <div class="right-content">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
          v-model:loading="loading"
          :finished="finished"
          :finished-text="recommendList.length ? $t('common.message.no-more') : ''"
          :loading-text="$t('common.message.loading')"
          :error-text="$t('common.message.loading-fail')"
          @load="onLoad"
          class="recommend-list"
        >
          <div class="recommend-item" v-for="(item, index) in recommendList" :key="index" @click="toDetail(item)">
            <div class="cover-content">
              <img class="rec-cover" :src="item.coverUri" alt="">
              <div class="duration">
                <img class="play-icon" :src="require('@/assets/images/play_mini.png')" alt="">
                <span>{{item.duration}}</span>
              </div>
            </div>
            <div class="recommend-content">
              <div class="recommend-title">{{ item.videoTitle || "" }}</div>
              <div class="recommend-time">{{  resetDate(item) }}</div>
            </div>
            <div class="living">
              <span>{{ $t('video.living')}}</span>
              <i></i>
            </div>
          </div>
        </van-list>
        <Nodata v-if="!recommendList.length && finished" />
        <template #pulling>
            <img
              :style="{height: '80%'}"
              :src="require('@/assets/gif/pullRefresh28.imageset/pullRefresh28.png')"
            />
          </template>
          <!-- 释放提示 -->
          <template #loosing>
            <img
              :style="{height: '80%'}"
              :src="require('@/assets/gif/pullRefresh28.imageset/pullRefresh28.png')"
            />
          </template>
          <template #loading>
            <loading></loading>
          </template>
      </van-pull-refresh>  
      
    </div>
  </popup>
</template>
<script>
import popup from "@/components/popup/index.vue";
import { getRecVideoList } from '@/api/video'
import Nodata from "@/components/nodata";
import Mixins from "@/mixins";
export default {
  data() {
    return {
      moreLiveList: [],
      visible: false,
      recommendList: [],
      page: 0,
      size: 10,
      finished: false,
      loading: false,
      refreshing: false,
      totalPages: 0
    }
  },
  mixins: [ Mixins ],
  components: {
    popup,
    Nodata
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:modelValue'],
  watch: {
    modelValue(val) {
      this.visible = val
    },
    visible(val) {
      this.$emit('update:modelValue', val)
    } 
  },
  mounted(){
    this.getRecVideoList('load')
  },
  methods:{
    async getRecVideoList(type){
      let params = {
        page: this.page,
        size: this.size,
        relatedColumn: '',
        videoId: '',
      }
      this.loading = true
      await getRecVideoList(params).then(res => {
        this.loading = false
        if(type === 'refresh'){
          this.refreshing = false;
          this.recommendList = res.data ? res.data.content : []
        }else{
          this.recommendList = this.recommendList.concat(res.data ? res.data.content : [])
        }
        if(this.totalPages <= this.page){
          this.finished = true
        }else{
          this.page++
        }
      })
    },
    toDetail(item){
      this.$emit('toDetail', item)
    },
    onRefresh(){
      this.getRecVideoList('refresh')
    },
    onLoad(){
      this.getRecVideoList('load')
    }
  }
}
</script>
<style lang="scss" scoped>
.right-content{
  width: 164px;
  padding: 0px 17px 0px;
  height: 100%;
  background: var(--background);
  overflow: auto;
  overflow-x: hidden;
}
.recommend-list{
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 20px 0;
}
.rela-title{
  font-size: 18px;
  font-weight: 600;
  color: var(--text_1st);
}
.recommend-item{
  position: relative;
  padding: 0px;
  width: 100%;
  background: var(--gray_05);
  margin-top: 8px;
  border-radius: 10px;
  
  .rec-cover{
    width: 100%;
    height: 100%;
    border-radius: 10px 10px 0px 0px;
    position: relative;
  }
  .recommend-content{
    padding: 8px;
  }
  .recommend-title{
    font-size: 14px;
    line-height: 22px;
    margin-bottom:  8px;
    color: var(--text_1st);
    font-weight: 600;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .duration{
    position: absolute;
    bottom: 8px;
    left: 8px;
    width: 60px;
    height: 20px;
    line-height: 20px;
    font-size: 11px;
    background: rgba(0,0,0,0.4);
    border-radius: 3px;
  }
  .cover-content{
    width: 100%;
    height: 91px;
    border-radius: 10px 10px 0px 0px;
    position: relative;
    color: var(--background);
  }
  .play-icon{
    vertical-align: bottom;
    width: 12px;
    padding: 4px 6px;
  }
}
.recommend-time{
  color: var(--text_3rd);
}
::v-deep .van-list__loading,.van-list__finished-text,.van-list__error-text {
    width: 100%;
}
::v-deep .right-content{
  .van-list__loading,.van-list__finished-text,.van-list__error-text {
    width: 100%;
  }
  .van-list__finished-text::before, .van-list__finished-text::after{
    width: 35px;
  }
}
.living {
  position: absolute;
  top: 5px;
  right: 5px;
  padding:2px 4px;
  background: var(--error);
  border-radius: 3px;
  font-size: 10px;
  font-weight: 400;
  text-align: center;
  color: var(--background_01);
  i{
    background: url("@/assets/images/living_ico.gif") no-repeat center;
    width: 8px;
    height: 7px;
    opacity: 1;
    display: inline-block;
    background-size: 100% 100%;
    margin-left: 2px;
  }
}

</style>